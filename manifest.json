{"manifest_version": 3, "name": "SlimPaneAI", "version": "0.0.1", "description": "A lightweight AI-powered browser extension with side panel chat and text enhancement features", "icons": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}, "action": {"default_icon": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}, "default_title": "Open SlimPaneAI"}, "side_panel": {"default_path": "panel.html"}, "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_end"}], "options_page": "options.html", "permissions": ["activeTab", "storage", "scripting", "contextMenus", "sidePanel"], "host_permissions": ["*://*/*", "file:///*"], "web_accessible_resources": [{"resources": ["icons/*", "pdf.mjs", "pdf.worker.js", "cmaps/*", "assets/*"], "matches": ["<all_urls>"]}]}