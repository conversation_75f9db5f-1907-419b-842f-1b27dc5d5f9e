# Icons

This directory contains the extension icons in different sizes.

Required sizes:
- icon-16.png (16x16) - Toolbar icon
- icon-32.png (32x32) - Windows taskbar
- icon-48.png (48x48) - Extension management page
- icon-128.png (128x128) - Chrome Web Store

The build script will create placeholder icons if none are provided.

To add custom icons:
1. Create PNG files with the required sizes
2. Name them according to the pattern above
3. Place them in this directory
