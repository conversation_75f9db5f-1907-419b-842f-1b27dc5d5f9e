{"name": "slimpane-ai", "version": "0.0.1", "description": "A lightweight AI-powered browser extension with side panel chat and text enhancement features", "type": "module", "scripts": {"dev": "vite build --watch", "build": "vite build && vite build --config vite.config.background.ts && vite build --config vite.config.content.ts", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.json", "lint": "eslint . --ext .js,.ts,.svelte", "lint:fix": "eslint . --ext .js,.ts,.svelte --fix", "format": "prettier --write .", "package": "npm run build && node scripts/package.js", "dev:package": "npm run build && node scripts/package.js && echo 'Extension packaged! Load dist/ folder in Chrome extensions.'"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^3.0.0", "@types/chrome": "^0.0.254", "@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "eslint-plugin-svelte": "^2.35.0", "prettier": "^3.2.0", "prettier-plugin-svelte": "^3.2.0", "svelte": "^4.2.8", "svelte-check": "^3.6.0", "typescript": "^5.3.0", "unocss": "^0.58.0", "vite": "^5.0.0"}, "dependencies": {"@mdit/plugin-katex": "^0.23.1", "@mozilla/readability": "^0.6.0", "highlight.js": "^11.11.1", "highlightjs-copy": "^1.0.6", "katex": "^0.16.9", "markdown-it": "^14.1.0", "pdfjs-dist": "^4.10.38", "svelte-i18n": "^4.0.0", "turndown": "^7.1.3"}}