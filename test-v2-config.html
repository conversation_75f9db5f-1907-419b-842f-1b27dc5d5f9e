<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - v2.0配置系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .author { color: #666; font-size: 14px; }
        .date { color: #999; font-size: 12px; }
        .tags { background: #f0f0f0; padding: 2px 6px; border-radius: 3px; margin: 2px; }
        .ad { background: red; color: white; padding: 10px; margin: 10px 0; }
        .sidebar { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        article { max-width: 800px; }
    </style>
</head>
<body>
    <article>
        <h1>测试文章标题：v2.0配置系统重构</h1>
        
        <div class="author">作者：张三</div>
        <div class="date">发布时间：2024-01-01 10:00:00</div>
        <div class="tags">
            <span class="tag">技术</span>
            <span class="tag">前端</span>
            <span class="tag">重构</span>
        </div>
        
        <div class="ad">这是一个广告，应该被移除</div>
        
        <p>这是文章的第一段内容。在v2.0版本中，我们完全重构了配置系统，移除了preserve配置，改为更有价值的元信息提取功能。</p>
        
        <p>元信息提取可以自动识别页面中的作者、发布时间、标签等结构化信息，并按照用户配置的模板格式化输出，为GPT提供更丰富的上下文信息。</p>
        
        <div class="sidebar">这是侧边栏内容，应该被移除</div>
        
        <p>新的配置系统支持两种模式：</p>
        <ul>
            <li><strong>Text模式</strong>：简单的文本提取，适合简单页面</li>
            <li><strong>Readability模式</strong>：智能提取 + 元信息，适合复杂页面</li>
        </ul>
        
        <p>模板系统也按模式进行了分类，用户可以根据不同的提取模式选择相应的预设模板。</p>
        
        <div class="ad">另一个广告，也应该被移除</div>
        
        <p>这是文章的最后一段。重构后的系统更加清晰、灵活，为用户提供了更好的内容提取体验。</p>
    </article>

    <script>
        console.log('测试页面加载完成');
        console.log('页面包含以下元信息：');
        console.log('- 作者:', document.querySelector('.author')?.textContent);
        console.log('- 时间:', document.querySelector('.date')?.textContent);
        console.log('- 标签:', Array.from(document.querySelectorAll('.tag')).map(el => el.textContent));
        console.log('- 广告数量:', document.querySelectorAll('.ad').length);
        console.log('- 侧边栏数量:', document.querySelectorAll('.sidebar').length);
    </script>
</body>
</html>
