<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { t } from '@/lib/i18n';
  import MetadataFieldsManagerModal from './MetadataFieldsManagerModal.svelte';
  import type { WebChatMetadataField } from '@/types/web-content-config';

  // Props
  export let enabled: boolean = false;
  export let fields: WebChatMetadataField[] = [];
  export let template: string = '';
  export let title: string = '🏷️ 元信息提取配置';
  export let description: string = '提取作者、时间、标签等结构化信息，增强GPT理解';
  export let showToggle: boolean = true;
  export let compact: boolean = false;

  // 事件派发器
  const dispatch = createEventDispatcher<{
    enabledChange: boolean;
    fieldsChange: WebChatMetadataField[];
    templateChange: string;
  }>();

  // 内部状态
  let showFieldManager = false;

  // 响应式更新
  $: dispatch('enabledChange', enabled);
  $: dispatch('fieldsChange', fields);
  $: dispatch('templateChange', template);

  // 响应式监听字段变化，自动更新模板
  $: {
    if (fields && fields.length >= 0) {
      // 检查是否应该自动更新模板
      const shouldUpdate = shouldAutoUpdateTemplate(template, fields);
      console.log('🔄 MetadataConfigSection: 响应式字段变化检查', {
        fieldsLength: fields.length,
        enabledCount: fields.filter(f => f.enabled).length,
        shouldUpdate,
        currentTemplate: JSON.stringify(template)
      });

      if (shouldUpdate) {
        const enabledFields = fields.filter(f => f.enabled);
        const newTemplate = enabledFields.map(field => `${field.name}: {${field.key}}`).join('\n');
        if (template !== newTemplate) {
          template = newTemplate;
          console.log('🔄 MetadataConfigSection: 响应式更新模板:', template);
        }
      }
    }
  }

  // 打开字段管理器
  function openFieldManager() {
    showFieldManager = true;
  }

  // 处理字段变更（从模态框接收字段数组）
  function handleFieldsChange(newFields: WebChatMetadataField[]) {
    console.log('🔄 MetadataConfigSection: 字段变更开始', {
      oldFields: fields,
      newFields: newFields,
      currentTemplate: template
    });

    fields = newFields;

    // 自动更新模板（如果模板为空或者是默认生成的）
    const shouldUpdate = shouldAutoUpdateTemplate(template, newFields);
    console.log('🔄 MetadataConfigSection: 是否应该更新模板:', shouldUpdate);

    if (shouldUpdate) {
      const enabledFields = newFields.filter(f => f.enabled);
      const newTemplate = enabledFields.map(field => `${field.name}: {${field.key}}`).join('\n');
      template = newTemplate;
      console.log('🔄 MetadataConfigSection: 字段变更，自动更新模板:', template);
    }

    // 不需要手动派发，响应式语句会自动派发
  }

  // 判断是否应该自动更新模板
  function shouldAutoUpdateTemplate(currentTemplate: string, currentFields: WebChatMetadataField[]): boolean {
    console.log('🔄 MetadataConfigSection: 检查是否应该更新模板', {
      currentTemplate: JSON.stringify(currentTemplate),
      currentFields: currentFields.map(f => ({ key: f.key, name: f.name, enabled: f.enabled }))
    });

    // 如果模板为空，总是自动更新
    if (!currentTemplate || currentTemplate === '') {
      console.log('🔄 MetadataConfigSection: 模板为空，应该更新');
      return true;
    }

    // 生成期望的模板
    const enabledFields = currentFields.filter(f => f.enabled);
    const expectedTemplate = enabledFields.map(field => `${field.name}: {${field.key}}`).join('\n');

    console.log('🔄 MetadataConfigSection: 模板对比', {
      currentTemplate: JSON.stringify(currentTemplate),
      expectedTemplate: JSON.stringify(expectedTemplate),
      enabledFieldsCount: enabledFields.length
    });

    // 简化判断逻辑：如果当前模板与期望模板不同，且当前模板看起来是自动生成的格式，则更新
    const isAutoGenerated = isAutoGeneratedTemplate(currentTemplate, currentFields);
    console.log('🔄 MetadataConfigSection: 模板是否为自动生成:', isAutoGenerated);

    // 如果是自动生成的格式，或者模板内容与期望不符，则更新
    return isAutoGenerated || currentTemplate !== expectedTemplate;
  }

  // 检查模板是否是自动生成的格式
  function isAutoGeneratedTemplate(currentTemplate: string, currentFields: WebChatMetadataField[]): boolean {
    if (!currentTemplate || !currentFields) {
      console.log('🔄 MetadataConfigSection: 模板或字段为空，认为是自动生成');
      return true;
    }

    // 常见的自动生成模板特征
    const autoGeneratedPatterns = [
      '', // 空模板
      '暂无启用的字段', // 无字段提示
      /^(\w+: \{\w+\}\n?)+$/, // 标准格式：字段名: {字段键}\n
      /^([\u4e00-\u9fa5\w\s]+: \{[\w]+\}\n?)+$/ // 支持中文字段名
    ];

    // 检查是否匹配任何自动生成的模式
    const isAutoPattern = autoGeneratedPatterns.some(pattern => {
      if (typeof pattern === 'string') {
        return currentTemplate === pattern;
      } else {
        return pattern.test(currentTemplate);
      }
    });

    console.log('🔄 MetadataConfigSection: 模板自动生成检查', {
      currentTemplate: JSON.stringify(currentTemplate),
      isAutoPattern,
      templateLength: currentTemplate.length
    });

    return isAutoPattern;
  }

  // 自动生成模板
  function autoGenerateTemplate() {
    const enabledFields = fields.filter(f => f.enabled);
    if (enabledFields.length === 0) {
      template = '';
      return;
    }

    template = enabledFields.map(field => `${field.name}: {${field.key}}`).join('\n');
    console.log('🔄 MetadataConfigSection: 手动生成模板:', template);
  }

  // 切换启用状态
  function toggleEnabled() {
    enabled = !enabled;
  }
</script>

<div class="metadata-config-section" class:compact>
  <!-- 标题和开关 -->
  {#if showToggle}
    <div class="feature-toggle-header">
      <div class="feature-info">
        <h4 class="subsection-title">{title}</h4>
        <div class="feature-description">{description}</div>
      </div>
      <label class="toggle-switch">
        <input type="checkbox" bind:checked={enabled} />
        <span class="toggle-slider"></span>
        <span class="toggle-label">{enabled ? $t('webChatConfig.metadataEnabled') : $t('webChatConfig.metadataDisabled')}</span>
      </label>
    </div>
  {:else}
    <div class="section-header-simple">
      <h4 class="subsection-title">{title}</h4>
      <div class="feature-description">{description}</div>
    </div>
  {/if}

  {#if enabled || !showToggle}
    <div class="metadata-config">
      <!-- 字段概览卡片 -->
      <div class="fields-overview-card">
        <div class="overview-header">
          <div class="overview-title">
            <h4>📋 {$t('webChatConfig.fieldsManager')}</h4>
            <div class="overview-badges">
              <span class="badge badge-total">{fields.length} {$t('webChatConfig.fieldsCount')}</span>
              <span class="badge badge-enabled">{fields.filter(f => f.enabled).length} {$t('webChatConfig.enabledFields')}</span>
            </div>
          </div>
          <button type="button" class="btn-manage" on:click={openFieldManager}>
            <span class="btn-icon">⚙️</span>
            <span>{$t('webChatConfig.manageFields')}</span>
          </button>
        </div>

        {#if fields.filter(field => field.enabled).length > 0}
          <div class="enabled-fields-preview">
            <div class="preview-label">{$t('webChatConfig.enabledFields')}:</div>
            <div class="fields-tags">
              {#each fields.filter(field => field.enabled) as field}
                <span class="field-tag">
                  <span class="tag-icon">🏷️</span>
                  {field.name}
                </span>
              {/each}
            </div>
          </div>
        {:else}
          <div class="no-fields-message">
            <span class="message-icon">⚠️</span>
            <span>{$t('webChatConfig.noEnabledFieldsDesc')}</span>
          </div>
        {/if}
      </div>

      <!-- 模板配置区域 -->
      <div class="template-config-section">
        <div class="section-header">
          <h4>📝 {$t('webChatConfig.outputTemplate')}</h4>
          <button type="button" class="btn-auto-generate" on:click={autoGenerateTemplate}>
            <span class="btn-icon">🔄</span>
            <span>{$t('webChatConfig.autoGenerate')}</span>
          </button>
        </div>

        <div class="template-editor">
          <textarea
            bind:value={template}
            placeholder="作者: &#123;author&#125;&#10;发布时间: &#123;date&#125;&#10;标签: &#123;tags&#125;"
            class="template-textarea"
            rows={compact ? "3" : "5"}
          ></textarea>
          <div class="template-help">
            <div class="help-item">
              <span class="help-icon">💡</span>
              <span>{$t('webChatConfig.templateDescription')}</span>
            </div>
            <div class="help-item">
              <span class="help-icon">📋</span>
              <span>{$t('common.examples')}: {$t('webChatConfig.authorField')}: &#123;author&#125;</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>

<!-- 字段管理器模态框 -->
<MetadataFieldsManagerModal
  bind:fields={fields}
  bind:isOpen={showFieldManager}
  on:fieldsChange={(e) => handleFieldsChange(e.detail)}
  on:close={() => showFieldManager = false}
/>

<style>
  .metadata-config-section {
    width: 100%;
  }

  .metadata-config-section.compact {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 0.5rem;
    border: 1px solid var(--border-secondary);
  }

  /* Feature toggle header styles - ensure consistency with parent */
  .feature-toggle-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
  }

  .feature-info {
    flex: 1;
  }

  .feature-info .subsection-title {
    margin: 0 0 0.5rem 0;
  }

  .feature-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.4;
  }

  .section-header-simple {
    margin-bottom: 1.5rem;
  }

  /* Toggle switch styles - consistent with parent component */
  .toggle-switch {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    user-select: none;
    flex-shrink: 0;
  }

  .toggle-switch input[type="checkbox"] {
    display: none;
  }

  .toggle-slider {
    position: relative;
    width: 3rem;
    height: 1.5rem;
    background: #cbd5e1;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .toggle-slider::before {
    content: '';
    position: absolute;
    top: 0.125rem;
    left: 0.125rem;
    width: 1.25rem;
    height: 1.25rem;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .toggle-switch input[type="checkbox"]:checked + .toggle-slider {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  }

  .toggle-switch input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(1.5rem);
  }

  .toggle-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    min-width: 3rem;
  }

  .toggle-switch:hover .toggle-slider {
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .metadata-config {
    margin-top: 1rem;
  }

  .fields-overview-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .overview-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
  }

  .overview-title h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
  }

  .overview-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .badge-total {
    background: #f3f4f6;
    color: #374151;
  }

  .badge-enabled {
    background: #dcfce7;
    color: #166534;
  }

  .btn-manage {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #6366f1;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    white-space: nowrap;
  }

  .btn-manage:hover {
    background: #4f46e5;
  }

  .btn-icon {
    font-size: 1rem;
  }

  .enabled-fields-preview {
    margin-top: 1rem;
  }

  .preview-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
  }

  .fields-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .field-tag {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    background: #fef3c7;
    color: #92400e;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .tag-icon {
    font-size: 0.875rem;
  }

  .no-fields-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.5rem;
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 1rem;
  }

  .message-icon {
    font-size: 1rem;
  }

  .template-config-section {
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: 0.75rem;
    padding: 1.5rem;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .section-header h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }

  .btn-auto-generate {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #10b981;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .btn-auto-generate:hover {
    background: #059669;
  }

  .template-editor {
    margin-top: 1rem;
  }

  .template-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-primary);
    border-radius: 0.5rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s;
  }

  .template-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .template-help {
    margin-top: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .help-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
  }

  .help-icon {
    font-size: 0.875rem;
  }

  .help-item code {
    background: var(--bg-tertiary);
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-family: monospace;
    font-size: 0.75rem;
  }

  @media (max-width: 768px) {
    .feature-toggle-header,
    .overview-header,
    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .overview-badges {
      margin-top: 0.5rem;
    }

    .fields-tags {
      margin-top: 0.5rem;
    }

    .btn-manage,
    .btn-auto-generate {
      align-self: flex-start;
    }
  }
</style>
