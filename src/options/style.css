/* Modern Options Page Styles with Theme Support */

/* CSS Variables for theming - 强制默认为浅色主题，避免无主题状态 */
:root,
html:not(.dark):not(.light),
html.light {
  /* Light theme defaults - 确保始终有主题 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;

  --text-primary: #111827;
  --text-secondary: #374151;
  --text-muted: #6b7280;

  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;

  --message-user-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --message-user-text: #ffffff;
  --message-assistant-bg: #f8fafc;
  --message-assistant-text: #1e293b;
  --message-assistant-border: #e2e8f0;

  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --input-focus-border: #3b82f6;
  --input-focus-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);

  --font-size-base: 0.875rem;
  --font-size-small: 0.75rem;
  --font-size-large: 1rem;

  --message-spacing: 1rem;
  --message-padding: 0.75rem 1rem;
}

/* Light theme */
.light {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-disabled: #f9fafb;

  --text-primary: #111827;
  --text-secondary: #374151;
  --text-muted: #6b7280;

  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;

  --message-user-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --message-user-text: #ffffff;
  --message-assistant-bg: #f8fafc;
  --message-assistant-text: #1e293b;
  --message-assistant-border: #e2e8f0;

  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --input-focus-border: #3b82f6;
  --input-focus-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Dark theme */
.dark {
  --bg-primary: #1f2937;
  --bg-secondary: #111827;
  --bg-tertiary: #374151;
  --bg-disabled: #374151;

  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;

  --border-primary: #374151;
  --border-secondary: #4b5563;

  --message-user-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --message-user-text: #ffffff;
  --message-assistant-bg: #374151;
  --message-assistant-text: #f9fafb;
  --message-assistant-border: #4b5563;

  --input-bg: #374151;
  --input-border: #4b5563;
  --input-focus-border: #3b82f6;
  --input-focus-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

* {
  box-sizing: border-box;
}

/* 分层过渡：只对关键元素应用过渡，避免全局过渡冲突 */
html, body {
  transition: background-color 0.15s ease-out, color 0.15s ease-out;
}

.settings-container,
.setting-item,
.theme-option,
.language-option,
.font-size-option,
.density-option,
button,
input,
select,
textarea {
  transition: background-color 0.15s ease-out, color 0.15s ease-out, border-color 0.15s ease-out;
}

/* Font size classes */
.font-small {
  --font-size-base: 0.75rem;
  --font-size-small: 0.6875rem;
  --font-size-large: 0.875rem;
}

.font-medium {
  --font-size-base: 0.875rem;
  --font-size-small: 0.75rem;
  --font-size-large: 1rem;
}

.font-large {
  --font-size-base: 1rem;
  --font-size-small: 0.875rem;
  --font-size-large: 1.125rem;
}

/* Message density classes */
.density-compact {
  --message-spacing: 0.5rem;
  --message-padding: 0.5rem 0.75rem;
}

.density-normal {
  --message-spacing: 0.75rem;
  --message-padding: 0.75rem 1rem;
}

.density-relaxed {
  --message-spacing: 1rem;
  --message-padding: 1rem 1.25rem;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.6;
  transition: background-color 0.2s ease, color 0.2s ease;
}

#app {
  min-height: 100vh;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px 0 rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  background: var(--bg-primary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
}

.btn-danger {
  background: #ef4444;
  color: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px 0 rgba(239, 68, 68, 0.4);
}

/* Form Elements */
.input-base {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-primary);
  border-radius: 0.75rem;
  outline: none;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: var(--input-bg);
  color: var(--text-primary);
}

.input-base:focus {
  border-color: var(--input-focus-border);
  box-shadow: var(--input-focus-shadow);
}

.select-base {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-primary);
  border-radius: 0.75rem;
  outline: none;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: var(--input-bg);
  color: var(--text-primary);
  cursor: pointer;
}

.select-base:focus {
  border-color: var(--input-focus-border);
  box-shadow: var(--input-focus-shadow);
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  padding: 2rem 0;
  margin-bottom: 2rem;
}

.card {
  background: var(--bg-primary);
  border-radius: 1rem;
  border: 1px solid var(--border-primary);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-secondary);
  background: var(--bg-tertiary);
}

.card-body {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.form-help {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}

/* Grid */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Utilities */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-4 {
  gap: 1rem;
}

.items-start {
  align-items: flex-start;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.text-xs {
  font-size: 0.75rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-gray-500 {
  color: var(--text-muted);
}

.text-gray-600 {
  color: var(--text-secondary);
}

.text-gray-900 {
  color: var(--text-primary);
}

.text-blue-600 {
  color: #2563eb;
}

.text-red-600 {
  color: #dc2626;
}

/* Model Cards */
.model-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.2s ease;
  position: relative;
}

.model-card:hover {
  border-color: var(--border-secondary);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.model-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.model-badge-openai {
  background: #dbeafe;
  color: #1e40af;
}

.model-badge-claude {
  background: #f3e8ff;
  color: #7c3aed;
}

.model-badge-gemini {
  background: #fef3c7;
  color: #d97706;
}

.model-badge-custom {
  background: #f0f9ff;
  color: #0369a1;
}

/* Additional utility classes for model cards */
.gap-6 {
  gap: 1.5rem;
}

.items-end {
  align-items: flex-end;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.w-4 {
  width: 1rem;
}

.h-4 {
  height: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.h-5 {
  height: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.h-6 {
  height: 1.5rem;
}

.w-8 {
  width: 2rem;
}

.h-8 {
  height: 2rem;
}

.w-12 {
  width: 3rem;
}

.h-12 {
  height: 3rem;
}

.w-16 {
  width: 4rem;
}

.h-16 {
  height: 4rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}

/* Focus styles */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--input-focus-border);
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background: rgba(59, 130, 246, 0.2);
  color: var(--text-primary);
}
