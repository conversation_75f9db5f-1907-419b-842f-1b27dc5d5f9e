/* Clean UI Styles */
.btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-secondary {
  background-color: #e5e7eb;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background-color: #d1d5db;
}

.input-base {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  outline: none;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
  background: white;
}

.input-base:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  background: var(--bg-secondary, #f9fafb);
  color: var(--text-primary, #1f2937);
  font-size: 14px;
  line-height: 1.5;
  transition: background-color 0.2s ease, color 0.2s ease;
}

html, body {
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

#app {
  height: 100vh !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  background: var(--bg-primary, white) !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

/* Utility classes */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-col {
  flex-direction: column;
}

.flex-1 {
  flex: 1 1 0%;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.h-full {
  height: 100%;
}

.w-full {
  width: 100%;
}

.text-center {
  text-align: center;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-900 {
  color: #111827;
}

.bg-white {
  background-color: white;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.bg-blue-100 {
  background-color: #dbeafe;
}

.text-blue-600 {
  color: #2563eb;
}

.border-b {
  border-bottom-width: 1px;
}

.border-gray-100 {
  border-color: #f3f4f6;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

.p-1\.5 {
  padding: 0.375rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.max-w-xs {
  max-width: 20rem;
}

.w-4 {
  width: 1rem;
}

.h-4 {
  height: 1rem;
}

.w-6 {
  width: 1.5rem;
}

.h-6 {
  height: 1.5rem;
}

.w-8 {
  width: 2rem;
}

.h-8 {
  height: 2rem;
}

.w-12 {
  width: 3rem;
}

.h-12 {
  height: 3rem;
}

.w-16 {
  width: 4rem;
}

.h-16 {
  height: 4rem;
}

.w-1 {
  width: 0.25rem;
}

.h-1 {
  height: 0.25rem;
}

.bg-blue-400 {
  background-color: #60a5fa;
}

.bg-green-400 {
  background-color: #4ade80;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.text-xs {
  font-size: 0.75rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.bg-purple-100 {
  background-color: #e9d5ff;
}

.text-purple-600 {
  color: #9333ea;
}

.bg-green-100 {
  background-color: #dcfce7;
}

.text-green-600 {
  color: #16a34a;
}

.border {
  border-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.p-8 {
  padding: 2rem;
}

.max-w-sm {
  max-width: 24rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-900 {
  background-color: #111827;
}

.hover\:bg-gray-800:hover {
  background-color: #1f2937;
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.focus-within\:border-gray-300:focus-within {
  border-color: #d1d5db;
}

.placeholder-gray-500::placeholder {
  color: #6b7280;
}

.min-h-\[52px\] {
  min-height: 52px;
}

.pr-12 {
  padding-right: 3rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.top-1\/2 {
  top: 50%;
}

.right-2 {
  right: 0.5rem;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.duration-200 {
  transition-duration: 200ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.bg-transparent {
  background-color: transparent;
}

.border-none {
  border-style: none;
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.resize-none {
  resize: none;
}

.text-center {
  text-align: center;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.overflow-hidden {
  overflow: hidden;
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.hover\:text-gray-600:hover {
  color: #4b5563;
}

.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

.hover\:text-red-500:hover {
  color: #ef4444;
}

.hover\:bg-red-50:hover {
  background-color: #fef2f2;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.message-enter {
  animation: slideInUp 0.3s ease-out;
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
}

.typing-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #6b7280;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Code highlighting - 避免干扰 highlight.js 主题 */
pre:not(.hljs) {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  /* 使用细滚动条 */
  scrollbar-width: thin;
  scrollbar-gutter: stable;
}

/* 普通 pre 元素的滚动条样式 */
pre:not(.hljs)::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

pre:not(.hljs)::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

pre:not(.hljs)::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

pre:not(.hljs)::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

code:not(.hljs):not(.inline-code) {
  background: #f1f5f9;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 13px;
}

/* 强制确保 highlight.js 样式不被覆盖 */
.hljs {
  display: block !important;
  overflow-x: auto !important;
  padding: 0.75rem !important;
  color: #c9d1d9 !important;
  background: #0d1117 !important;
  border-radius: 0.5rem !important;
  margin: 0 !important;
  /* 使用细滚动条，防止抖动 */
  scrollbar-width: thin !important;
  scrollbar-gutter: stable !important;
}

/* 自定义代码块滚动条样式 */
.hljs::-webkit-scrollbar {
  height: 8px !important;
  width: 8px !important;
}

.hljs::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 4px !important;
}

.hljs::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3) !important;
  border-radius: 4px !important;
}

.hljs::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5) !important;
}

/* 确保父容器定位上下文 */
.hljs-copy-wrapper {
  position: relative !important;
  border-radius: 0.5rem !important;
  overflow: hidden !important;
  margin: 0.5rem 0 !important;
  min-height: fit-content !important;
  display: block !important;
  border: none !important;
}

/* 确保 hljs 元素保持原有的圆角和样式 */
.hljs-copy-wrapper .hljs {
  margin: 0 !important;
  /* 恢复代码块的圆角 */
  border-radius: 0.5rem !important;
  /* 确保代码块填满容器 */
  width: 100% !important;
  box-sizing: border-box !important;
  /* 为复制按钮预留更多空间 */
  padding-right: 5rem !important;
}

/* 重新定位按钮容器 - 直接定位到代码块内部 */
.hljs-copy-container {
  position: absolute !important;
  top: 0.75rem !important;
  right: 0.75rem !important;
  height: auto !important;
  width: auto !important;
  display: block !important;
  z-index: 10 !important;
  transform: none !important;
  transition: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.hljs-copy-button,
.hljs-copy-button[data-copied="true"],
.hljs-copy-button:not([data-copied="true"]) {
  /* 重置所有默认样式 */
  all: unset !important;
  /* 直接绝对定位按钮到代码块右上角 */
  position: absolute !important;
  top: 0.75rem !important;
  right: 0.75rem !important;
  margin: 0 !important;
  transform: none !important;
  width: auto !important;
  height: auto !important;
  padding: 0.25rem 0.5rem !important;
  background: rgba(55, 65, 81, 0.9) !important;
  border: 1px solid rgba(156, 163, 175, 0.3) !important;
  border-radius: 0.25rem !important;
  color: #f3f4f6 !important;
  font-size: 0.7rem !important;
  font-weight: 500 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  cursor: pointer !important;
  transition: all 0.15s ease !important;
  text-indent: 0 !important;
  overflow: visible !important;
  display: inline-block !important;
  box-sizing: border-box !important;
  opacity: 0.85 !important;
  z-index: 20 !important;
  /* 添加轻微阴影增强层次感 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

.hljs-copy-button::before {
  display: none !important;
}

.hljs-copy-button:hover {
  background: rgba(75, 85, 99, 0.95) !important;
  border-color: rgba(156, 163, 175, 0.5) !important;
  opacity: 1 !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3) !important;
}

/* 确保容器不会隐藏 */
.hljs-copy-container[data-autohide="true"] {
  transform: none !important;
}

/* 移除调试样式，容器现在不需要特殊样式 */

/* Essential Tailwind-like utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.gap-3 {
  gap: 0.75rem;
}

.h-full {
  height: 100%;
}

.h-8 {
  height: 2rem;
}

.h-4 {
  height: 1rem;
}

.w-full {
  width: 100%;
}

.w-8 {
  width: 2rem;
}

.w-4 {
  width: 1rem;
}

.bg-white {
  background-color: white;
}

.bg-blue-100 {
  background-color: #dbeafe;
}

.bg-green-100 {
  background-color: #dcfce7;
}

.bg-purple-100 {
  background-color: #f3e8ff;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.bg-blue-600 {
  background-color: #2563eb;
}

.bg-green-600 {
  background-color: #16a34a;
}

.bg-purple-600 {
  background-color: #9333ea;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.text-gray-900 {
  color: #111827;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-500 {
  color: #6b7280;
}

.text-white {
  color: white;
}

.p-4 {
  padding: 1rem;
}

.p-8 {
  padding: 2rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.border {
  border-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-y-auto {
  overflow-y: auto;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-10 {
  z-index: 10;
}

.max-w-sm {
  max-width: 24rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}



/* CSS Variables for theming - fallback values */
:root {
  /* Light theme defaults */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;

  --text-primary: #111827;
  --text-secondary: #374151;
  --text-muted: #6b7280;

  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;

  --message-user-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --message-user-text: #ffffff;
  --message-assistant-bg: #f8fafc;
  --message-assistant-text: #1e293b;
  --message-assistant-border: #e2e8f0;

  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --input-focus-border: #3b82f6;
  --input-focus-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);

  --font-size-base: 0.875rem;
  --font-size-small: 0.75rem;
  --font-size-large: 1rem;

  --message-spacing: 0.375rem;
  --message-padding: 0.5rem 0.75rem;
}

/* Light theme */
.light {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-disabled: #f9fafb;

  --text-primary: #111827;
  --text-secondary: #374151;
  --text-muted: #6b7280;

  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;

  --message-user-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --message-user-text: #ffffff;
  --message-assistant-bg: #f8fafc;
  --message-assistant-text: #1e293b;
  --message-assistant-border: #e2e8f0;

  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --input-focus-border: #3b82f6;
  --input-focus-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Dark theme */
.dark {
  --bg-primary: #1f2937;
  --bg-secondary: #111827;
  --bg-tertiary: #374151;
  --bg-disabled: #374151;

  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;

  --border-primary: #374151;
  --border-secondary: #4b5563;

  --message-user-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --message-user-text: #ffffff;
  --message-assistant-bg: #374151;
  --message-assistant-text: #f9fafb;
  --message-assistant-border: #4b5563;

  --input-bg: #374151;
  --input-border: #4b5563;
  --input-focus-border: #3b82f6;
  --input-focus-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Smooth transitions for theme changes */
body, .panel-container, .message-bubble, .input-container, button, select, input, textarea {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}

/* KaTeX math rendering - force correct fonts */
.katex, .katex * {
  font-family: KaTeX_Main, "Times New Roman", serif !important;
  transition: none !important;
}

.katex .sqrt-sign {
  font-family: KaTeX_Size1, KaTeX_Size2, KaTeX_Size3, KaTeX_Size4, KaTeX_Main, "Times New Roman", serif !important;
}

.katex .mord, .katex .mop, .katex .mbin, .katex .mrel, .katex .mopen, .katex .mclose, .katex .mpunct {
  font-family: KaTeX_Main, "Times New Roman", serif !important;
}

.katex .sizing, .katex .fontsize-ensurer {
  font-family: KaTeX_Size1, KaTeX_Size2, KaTeX_Size3, KaTeX_Size4, KaTeX_Main, serif !important;
}

/* Final override - ensure KaTeX styles are not overridden by UnoCSS or other frameworks */
.katex, .katex span, .katex .base, .katex .strut, .katex .vlist, .katex .vlist-t, .katex .vlist-r {
  font-family: KaTeX_Main, "Times New Roman", serif !important;
  font-style: normal !important;
  font-weight: normal !important;
  transition: none !important;
}

.katex .sqrt-sign, .katex .sqrt > .vlist-t > .vlist-r > .vlist > span > .sqrt-sign {
  font-family: KaTeX_Size1, KaTeX_Size2, KaTeX_Size3, KaTeX_Size4, KaTeX_Main, "Times New Roman", serif !important;
  font-style: normal !important;
  font-weight: normal !important;
}

/* Font size classes */
.font-small {
  --font-size-base: 0.75rem;
  --font-size-small: 0.6875rem;
  --font-size-large: 0.875rem;
}

.font-medium {
  --font-size-base: 0.875rem;
  --font-size-small: 0.75rem;
  --font-size-large: 1rem;
}

.font-large {
  --font-size-base: 1rem;
  --font-size-small: 0.875rem;
  --font-size-large: 1.125rem;
}

/* Message density classes */
.density-compact {
  --message-spacing: 0.25rem;
  --message-padding: 0.375rem 0.625rem;
}

.density-normal {
  --message-spacing: 0.375rem;
  --message-padding: 0.5rem 0.75rem;
}

.density-relaxed {
  --message-spacing: 0.5rem;
  --message-padding: 0.625rem 0.875rem;
}



/* Scrollbar theming */
::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Code block theming */
pre {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

code {
  background: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

/* Typing indicator theming */
.typing-dot {
  background: var(--text-muted) !important;
}
