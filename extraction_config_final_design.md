# 内容提取配置最终设计方案

## 背景与目标

基于对原始设计的深度分析和实际使用需求，制定最终的配置方案。核心改进包括：

1. **移除低效配置** - 删除意义有限的 preserve 配置
2. **模式化设计** - 明确区分 Text 模式和 Readability 模式的配置结构
3. **增强元信息提取** - 在 Readability 模式下提供丰富的元信息提取能力
4. **简化用户体验** - 降低配置复杂度，提供清晰的配置边界

## 核心设计原则

### 1. 模式分离原则
- **Text 模式**：专注内容清洁，只支持 remove 配置
- **Readability 模式**：智能提取 + 元信息增强，支持 remove 和 metadata 配置

### 2. 配置层次原则
- 全局配置：所有网站的通用规则
- 域名配置：特定域名的专用规则
- 模板配置：按使用场景分类的预设配置

### 3. 渐进增强原则
- 基础功能开箱即用
- 高级功能可选配置
- 专家级功能深度定制

## 配置数据结构

### 2.1 顶层结构
```
{
  "version": "2.0",
  "mode": "text" | "readability",
  "global": {...},
  "domains": {...},
  "templates": {...}
}
```

### 2.2 全局配置 (global)

#### Text 模式全局配置
```
{
  "remove": [选择器数组] // 要移除的元素
}
```

#### Readability 模式全局配置
```
{
  "remove": [选择器数组],
  "metadata": {
    "enabled": boolean,
    "selectors": {
      "author": "选择器字符串",
      "date": "选择器字符串", 
      "tags": "选择器字符串",
      "title": "选择器字符串",
      // 扩展字段...
    },
    "format": {
      "template": "格式模板字符串",
      "separator": "数组分隔符",
      "includeEmpty": boolean
    }
  },
  "readabilityOptions": {
    "charThreshold": number,
    "keepClasses": boolean,
    "preserveLinks": boolean,
    "maxElemsToDivide": number
  }
}
```

### 2.3 域名配置 (domains)

#### 通用域名配置结构
```
"域名": {
  "name": "网站名称",
  "remove": [域名特定的移除规则],
  
  // 仅 Readability 模式包含
  "metadata": {
    "enabled": boolean,
    "selectors": {...},
    "format": {...}
  }
}
```

### 2.4 模板配置 (templates)

#### 按模式分类的模板结构
```
{
  "text-mode": {
    "模板ID": {
      "name": "模板名称",
      "description": "模板描述", 
      "mode": "text",
      "config": {
        "remove": [移除规则]
      }
    }
  },
  
  "readability-mode": {
    "模板ID": {
      "name": "模板名称",
      "description": "模板描述",
      "mode": "readability", 
      "config": {
        "remove": [移除规则],
        "metadata": {元信息配置}
      }
    }
  }
}
```

## 界面设计规范

### 3.1 模式选择区
```
🔄 内容提取模式
○ Text 模式 - 纯文本提取，快速简洁
  适用场景：个人博客、简单页面、快速浏览
  
○ Readability 模式 - 智能提取 + 元信息增强
  适用场景：新闻文章、技术博客、正式文档
```

### 3.2 基础配置区

#### Text 模式界面
```
📝 内容清洁配置
移除干扰元素：
┌─────────────────────────────────────────────────┐
│ .ad, .sidebar, nav, footer, .comment           │
│ [快捷按钮: 广告] [导航] [评论] [侧边栏]        │
└─────────────────────────────────────────────────┘
```

#### Readability 模式界面  
```
📝 内容清洁配置
移除干扰元素：
┌─────────────────────────────────────────────────┐
│ .ad, .sidebar, nav, footer, .comment           │
│ [快捷按钮: 广告] [导航] [评论] [侧边栏]        │
└─────────────────────────────────────────────────┘

📋 元信息提取
☑ 启用元信息提取

作者信息：┌───────────────────────────────────────┐
         │ .author, .username, .byline          │
         └───────────────────────────────────────┘

发布时间：┌───────────────────────────────────────┐
         │ .date, .time, .published             │
         └───────────────────────────────────────┘

标签分类：┌───────────────────────────────────────┐
         │ .tag, .category, .label              │
         └───────────────────────────────────────┘
```

### 3.3 高级配置区

#### 域名规则管理
```
📍 域名特定规则
┌─ 域名规则列表 ─────────────────────────────────────┐
│                                                   │
│ zhihu.com - 知乎               [编辑] [删除] [禁用] │
│ └─ 移除：广告模块、悬浮工具栏、侧边推荐              │
│ └─ 元信息：作者、时间、话题标签 (仅Readability模式) │
│                                                   │
│ csdn.net - CSDN技术社区        [编辑] [删除] [禁用] │  
│ └─ 移除：工具栏、评论区、推荐内容                   │
│ └─ 元信息：作者、发布时间、技术标签                │
│                                                   │
│ [+ 添加域名规则]                                  │
└───────────────────────────────────────────────────┘
```

#### 预设模板管理
```
🎨 预设模板
┌─ Text模式模板 ──────────────────────────────────────┐
│ [知乎纯净] [CSDN简洁] [微博清爽] [论坛精简]         │
│ [新闻网站] [购物网站] [文档站点] [个人博客]         │
└───────────────────────────────────────────────────┘

┌─ Readability模式模板 ──────────────────────────────┐
│ [技术博客] [新闻媒体] [学术论文] [产品文档]         │
│ [社交媒体] [论坛讨论] [官方公告] [用户手册]         │
└───────────────────────────────────────────────────┘

[保存当前配置为模板] [导入模板文件] [导出所有配置]
```

## 预设配置方案

### 4.1 Text 模式预设

#### 4.1.1 通用网站清洁模板
- **知乎纯净版**：移除广告、推荐、悬浮按钮等干扰元素
- **CSDN简洁版**：专注技术内容，移除工具栏、评论、推荐
- **微博清爽版**：保留核心内容，移除侧边栏、广告、热搜
- **论坛精简版**：适用于各类论坛，保留主题内容和用户信息

#### 4.1.2 垂直领域模板  
- **新闻网站**：移除广告、相关推荐、热点新闻
- **购物网站**：专注商品信息，移除推荐、广告、评价
- **文档站点**：保留技术文档核心内容，移除导航、侧边栏
- **个人博客**：适用于个人博客，移除小工具、友链

### 4.2 Readability 模式预设

#### 4.2.1 内容创作类
- **技术博客**：提取作者、发布时间、技术标签、阅读量
- **新闻媒体**：提取记者、来源、发布时间、新闻分类
- **学术论文**：提取作者、期刊、发表时间、关键词
- **产品文档**：提取版本、更新时间、产品分类

#### 4.2.2 社交互动类
- **社交媒体**：提取用户名、发布时间、话题标签、互动数据
- **论坛讨论**：提取发帖人、回复时间、版块分类、热度指标
- **问答平台**：提取提问者、回答时间、问题分类、投票数
- **评论系统**：提取评论者、评论时间、层级关系

## 技术实现要点

### 5.1 配置解析与合并

#### 配置优先级
1. 域名特定配置（最高优先级）
2. 全局配置  
3. 默认配置（最低优先级）

#### 合并策略
- **remove 规则**：累加合并（全局 + 域名特定）
- **metadata 配置**：域名配置完全覆盖全局配置
- **format 模板**：域名配置优先，未定义时使用全局配置

### 5.2 模式切换处理

#### 模式检测
- 根据配置文件的 mode 字段确定当前模式
- 界面根据模式动态显示/隐藏相关配置项
- 模式切换时自动过滤不兼容的配置

#### 配置校验
- Text 模式：只保留 remove 配置，忽略 metadata 相关配置
- Readability 模式：完整配置校验，检查选择器语法正确性
- 域名配置：继承模式约束，确保配置一致性

### 5.3 元信息提取逻辑

#### 提取策略
- 多选择器支持：按优先级依次尝试，直到找到有效内容
- 标签处理：支持提取多个标签，自动去重和排序
- 时间解析：支持多种时间格式，统一转换为标准格式
- 数值处理：提取数字类数据（点赞、阅读量等），支持格式化

#### 格式化模板
- 变量替换：支持 {author}、{date}、{tags} 等占位符
- 条件显示：空值处理，可配置是否显示空字段
- 自定义分隔符：数组类数据的连接符可配置
- 多语言支持：模板文本支持多语言配置

## 用户体验优化

### 6.1 配置向导

#### 新用户引导
1. **模式选择指导**：根据使用场景推荐合适的模式
2. **预设模板推荐**：基于域名自动推荐相关模板
3. **一键应用**：提供常用网站的一键配置功能

#### 配置测试
1. **实时预览**：配置修改后立即展示提取效果
2. **对比模式**：同时展示配置前后的效果对比
3. **批量测试**：支持多个URL的批量配置测试

### 6.2 配置管理

#### 配置备份
- 自动备份：配置变更时自动创建备份点
- 版本管理：支持配置版本的查看和回滚
- 云端同步：支持配置在多设备间同步

#### 配置分享
- 导出功能：支持导出单个域名或完整配置
- 导入功能：支持从文件或URL导入配置
- 社区分享：优质配置可分享到配置库

## 扩展性设计

### 7.1 插件化架构
- **选择器插件**：支持自定义选择器类型和匹配逻辑
- **元信息提取插件**：支持扩展新的元信息类型
- **格式化插件**：支持自定义输出格式和模板引擎

### 7.2 API 接口设计
- **配置管理 API**：提供配置的增删改查接口
- **提取功能 API**：支持通过 API 调用内容提取功能
- **模板管理 API**：支持模板的分享和订阅功能

### 7.3 数据分析支持
- **使用统计**：记录配置使用频率和成功率
- **效果分析**：分析不同配置的提取效果
- **优化建议**：基于统计数据提供配置优化建议

## 实施计划

### 阶段一：核心重构（2周）
- [ ] 更新配置数据结构和校验逻辑
- [ ] 实现模式切换和配置合并功能
- [ ] 重构元信息提取和格式化逻辑

### 阶段二：界面改版（2周）
- [ ] 重新设计配置界面，实现模式化显示
- [ ] 优化域名规则和模板管理界面
- [ ] 实现配置测试和预览功能

### 阶段三：预设配置（1周）
- [ ] 制作 Text 模式的预设模板库
- [ ] 制作 Readability 模式的预设模板库
- [ ] 完善主流网站的域名特定配置

### 阶段四：测试优化（1周）
- [ ] 全面测试主流网站的提取效果
- [ ] 优化配置合并和错误处理逻辑
- [ ] 完善文档和用户指南

## 成功指标

### 功能指标
- 支持 Text 和 Readability 两种模式，配置互不干扰
- 提供 20+ 主流网站的预设配置
- 支持 10+ 种元信息类型的提取
- 配置导入导出功能完整可用

### 体验指标
- 新用户 5 分钟内完成基础配置
- 配置界面响应时间 < 200ms
- 配置错误率 < 5%
- 用户满意度 > 85%

### 技术指标
- 配置文件向后兼容 v1.0 版本
- 代码测试覆盖率 > 90%
- 内存使用量较当前版本无明显增加
- 提取速度保持当前水平或有所提升